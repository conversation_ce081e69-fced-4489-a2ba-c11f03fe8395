[{"directory": "D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/arm64-v8a", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=aarch64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -ID:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles\\appmodules.dir\\D_\\ReactNative\\xm\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\ReactNative\\xm\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\ReactNative\\xm\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/arm64-v8a", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=aarch64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -ID:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\ReactNative\\xm\\AwesomeProject\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\ReactNative\\xm\\AwesomeProject\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}]