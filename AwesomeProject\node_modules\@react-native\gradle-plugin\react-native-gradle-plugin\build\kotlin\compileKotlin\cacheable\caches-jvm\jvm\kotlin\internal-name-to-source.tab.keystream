!com/facebook/react/ReactExtension+com/facebook/react/ReactExtension$Companioncom/facebook/react/ReactPlugin&com/facebook/react/ReactPlugin$apply$1(com/facebook/react/ReactPlugin$apply$1$1*com/facebook/react/ReactPlugin$apply$1$2$1&com/facebook/react/ReactPlugin$apply$2Kcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenSchemaTask$1Mcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenSchemaTask$1$1Ncom/facebook/react/ReactPlugin$configureCodegen$generateCodegenArtifactsTask$1Pcom/facebook/react/ReactPlugin$configureCodegen$generateCodegenArtifactsTask$1$11com/facebook/react/ReactPlugin$configureCodegen$1Mcom/facebook/react/ReactPlugin$configureAutolinking$generatePackageListTask$1acom/facebook/react/ReactPlugin$configureAutolinking$generateAutolinkingNewArchitectureFilesTask$17com/facebook/react/ReactPlugin$configureAutolinking$1$1)com/facebook/react/ReactRootProjectPlugin1com/facebook/react/ReactRootProjectPlugin$apply$1&com/facebook/react/TaskConfigurationKtGcom/facebook/react/TaskConfigurationKt$configureReactTasks$bundleTask$1<com/facebook/react/TaskConfigurationKt$configureReactTasks$1<com/facebook/react/TaskConfigurationKt$configureReactTasks$21com/facebook/react/internal/PrivateReactExtension*com/facebook/react/tasks/BundleHermesCTask7com/facebook/react/tasks/BundleHermesCTask$runCommand$14com/facebook/react/tasks/BundleHermesCTask$sources$1Dcom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTaskocom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCmakeFileContent$libraryIncludes$1ncom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCmakeFileContent$libraryModules$1icom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCppFileContent$cppIncludes$1ycom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCppFileContent$cppTurboModuleJavaProviders$1xcom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCppFileContent$cppTurboModuleCxxProviders$2ucom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCppFileContent$cppComponentDescriptors$2wcom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$generateCppFileContent$cppComponentDescriptors$2$1Ncom/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask$Companion5com/facebook/react/tasks/GenerateCodegenArtifactsTask2com/facebook/react/tasks/GenerateCodegenSchemaTaskAcom/facebook/react/tasks/GenerateCodegenSchemaTask$jsInputFiles$10com/facebook/react/tasks/GeneratePackageListTaskHcom/facebook/react/tasks/GeneratePackageListTask$composePackageImports$1Icom/facebook/react/tasks/GeneratePackageListTask$composePackageInstance$1:com/facebook/react/tasks/GeneratePackageListTask$CompanionUcom/facebook/react/tasks/GeneratePackageListTask$Companion$interpolateDynamicValues$15com/facebook/react/tasks/internal/BuildCodegenCLITask?com/facebook/react/tasks/internal/BuildCodegenCLITask$CompanionBcom/facebook/react/tasks/internal/BuildCodegenCLITask$inputFiles$1Ccom/facebook/react/tasks/internal/BuildCodegenCLITask$outputFiles$12com/facebook/react/tasks/internal/PrepareBoostTask?com/facebook/react/tasks/internal/PrepareBoostTask$taskAction$11com/facebook/react/tasks/internal/PrepareGlogTask>com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$1@com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$1$1>com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$2@com/facebook/react/tasks/internal/PrepareGlogTask$taskAction$2$10com/facebook/react/tasks/internal/PrepareJSCTaskDcom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$jscAAR$1Ecom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$soFiles$1Icom/facebook/react/tasks/internal/PrepareJSCTask$taskAction$headerFiles$1=com/facebook/react/tasks/internal/PrepareJSCTask$taskAction$1?com/facebook/react/tasks/internal/PrepareJSCTask$taskAction$1$1:com/facebook/react/tasks/internal/PreparePrefabHeadersTaskKcom/facebook/react/tasks/internal/PreparePrefabHeadersTask$taskAction$1$1$1@com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry-com/facebook/react/utils/AgpConfiguratorUtilsVcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1Xcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1$1Zcom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForLibraries$1$1$1Ncom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1Pcom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1$1Rcom/facebook/react/utils/AgpConfiguratorUtils$configureNamespaceForLibraries$1$1$1Ycom/facebook/react/utils/AgpConfiguratorUtils$configureBuildConfigFieldsForApp$action$1$1Jcom/facebook/react/utils/AgpConfiguratorUtils$configureDevPorts$action$1$1/com/facebook/react/utils/AgpConfiguratorUtilsKt,com/facebook/react/utils/BackwardCompatUtils(com/facebook/react/utils/DependencyUtils@com/facebook/react/utils/DependencyUtils$configureRepositories$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$1Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$1$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$2Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$2$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$3Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$3$1Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$3$2Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$4Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$4$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$5Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$5$1Dcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$6Fcom/facebook/react/utils/DependencyUtils$configureRepositories$1$1$6$1@com/facebook/react/utils/DependencyUtils$configureDependencies$1Bcom/facebook/react/utils/DependencyUtils$configureDependencies$1$1Dcom/facebook/react/utils/DependencyUtils$configureDependencies$1$1$1;com/facebook/react/utils/DependencyUtils$mavenRepoFromUrl$2;com/facebook/react/utils/DependencyUtils$mavenRepoFromUrl$1;com/facebook/react/utils/DependencyUtils$mavenRepoFromURI$2;com/facebook/react/utils/DependencyUtils$mavenRepoFromURI$1$com/facebook/react/utils/FileUtilsKt-com/facebook/react/utils/JdkConfiguratorUtilsGcom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1Icom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$1Icom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$2Rcom/facebook/react/utils/JdkConfiguratorUtils$configureJavaToolChains$1$action$1$1-com/facebook/react/utils/NdkConfiguratorUtilsGcom/facebook/react/utils/NdkConfiguratorUtils$configureReactNativeNdk$1Icom/facebook/react/utils/NdkConfiguratorUtils$configureReactNativeNdk$1$1"com/facebook/react/utils/PathUtils=com/facebook/react/utils/PathUtils$projectPathToLibraryName$1%com/facebook/react/utils/ProjectUtils&com/facebook/react/utils/PropertyUtils                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  