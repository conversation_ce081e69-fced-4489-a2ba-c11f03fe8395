_react-native-gradle-plugin/src/main/kotlin/com/facebook/react/internal/PrivateReactExtension.ktWreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactRootProjectPlugin.ktZreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/BackwardCompatUtils.kthreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PreparePrefabHeadersTask.kt`react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareBoostTask.ktPreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PathUtils.ktcreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/BuildCodegenCLITask.kt^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareJSCTask.kt`react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenSchemaTask.ktPreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/FileUtils.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/AgpConfiguratorUtils.ktrreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask.ktSreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/ProjectUtils.ktOreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactExtension.ktLreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactPlugin.ktVreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/DependencyUtils.kt_react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareGlogTask.ktRreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/TaskConfiguration.kt^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GeneratePackageListTask.ktnreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry.ktTreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PropertyUtils.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/JdkConfiguratorUtils.ktcreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenArtifactsTask.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/NdkConfiguratorUtils.ktXreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/BundleHermesCTask.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           