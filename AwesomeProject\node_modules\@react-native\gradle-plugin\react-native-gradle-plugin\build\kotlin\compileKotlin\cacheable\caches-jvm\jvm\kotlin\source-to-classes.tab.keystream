Oreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactExtension.ktLreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactPlugin.ktWreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactRootProjectPlugin.ktRreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/TaskConfiguration.kt_react-native-gradle-plugin/src/main/kotlin/com/facebook/react/internal/PrivateReactExtension.ktXreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/BundleHermesCTask.ktrreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask.ktcreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenArtifactsTask.kt`react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenSchemaTask.kt^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GeneratePackageListTask.ktcreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/BuildCodegenCLITask.kt`react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareBoostTask.kt_react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareGlogTask.kt^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareJSCTask.kthreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PreparePrefabHeadersTask.ktnreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/AgpConfiguratorUtils.ktZreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/BackwardCompatUtils.ktVreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/DependencyUtils.ktPreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/FileUtils.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/JdkConfiguratorUtils.kt[react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/NdkConfiguratorUtils.ktPreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PathUtils.ktSreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/ProjectUtils.ktTreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PropertyUtils.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           