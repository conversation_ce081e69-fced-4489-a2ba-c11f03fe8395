{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "appmodules", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-b4d6a90bee5777c65fd4.json", "name": "appmodules", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/arm64-v8a", "source": "D:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}