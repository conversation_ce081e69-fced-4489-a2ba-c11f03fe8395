# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || CMakeFiles/appmodules.dir

build CMakeFiles/appmodules.dir/D_/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug D$:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\ReactNative\xm\AwesomeProject\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1
  INCLUDES = -ID:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\ReactNative\xm\AwesomeProject\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\ReactNative\xm\AwesomeProject\android\app\build\intermediates\cxx\Debug\q4252k1g\obj\armeabi-v7a\libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1
  INCLUDES = -ID:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\ReactNative\xm\AwesomeProject\android\app\build\intermediates\cxx\Debug\q4252k1g\obj\armeabi-v7a\libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\ReactNative\xm\AwesomeProject\android\app\build\intermediates\cxx\Debug\q4252k1g\obj\armeabi-v7a\libappmodules.so

build D$:/ReactNative/xm/AwesomeProject/android/app/build/intermediates/cxx/Debug/q4252k1g/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug CMakeFiles/appmodules.dir/D_/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/xuexi/Android$ Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/8b60b4f75564ac53567672df7a1c9a73/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.10.2/transforms/e7a87e28456e3cd3dea6e54dbe4d1714/transformed/react-android-0.76.3-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = D:\ReactNative\xm\AwesomeProject\android\app\build\intermediates\cxx\Debug\q4252k1g\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = D:\ReactNative\xm\AwesomeProject\android\app\build\intermediates\cxx\Debug\q4252k1g\obj\armeabi-v7a\libappmodules.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\xm\AwesomeProject\android\app\.cxx\Debug\q4252k1g\armeabi-v7a && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\xm\AwesomeProject\android\app\.cxx\Debug\q4252k1g\armeabi-v7a && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\xm\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\xm\AwesomeProject\android\app\.cxx\Debug\q4252k1g\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/ReactNative/xm/AwesomeProject/android/app/build/intermediates/cxx/Debug/q4252k1g/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony D$:/ReactNative/xm/AwesomeProject/android/app/build/intermediates/cxx/Debug/q4252k1g/obj/armeabi-v7a/libappmodules.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a

build all: phony D$:/ReactNative/xm/AwesomeProject/android/app/build/intermediates/cxx/Debug/q4252k1g/obj/armeabi-v7a/libappmodules.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/cmake.verify_globs | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
