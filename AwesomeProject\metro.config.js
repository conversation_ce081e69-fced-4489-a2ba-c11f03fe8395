const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  // 启用缓存以加速构建
  cacheStores: [
    {
      type: 'FileStore',
      root: require('path').join(__dirname, '.metro-cache'),
    },
  ],
  // 优化转换器性能
  transformer: {
    // 启用内联需要优化
    inlineRequires: true,
  },
  // 优化解析器性能
  resolver: {
    // 启用符号链接支持
    unstable_enableSymlinks: true,
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
