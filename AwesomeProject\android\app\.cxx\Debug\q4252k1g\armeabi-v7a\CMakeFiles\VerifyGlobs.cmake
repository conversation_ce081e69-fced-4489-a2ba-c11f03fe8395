# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:37 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/ReactNative/xm/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# input_SRC at D:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:37 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/ReactNative/xm/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/ReactNative/xm/AwesomeProject/android/app/.cxx/Debug/q4252k1g/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()
